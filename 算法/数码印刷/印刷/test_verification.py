#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_original_code():
    """测试原始代码的逻辑"""
    # 原始代码逻辑
    a = 1
    b = 2
    c = a + b
    
    # 验证结果
    expected = 3
    actual = c
    
    print(f"测试结果:")
    print(f"a = {a}")
    print(f"b = {b}")
    print(f"c = a + b = {c}")
    print(f"期望值: {expected}")
    print(f"实际值: {actual}")
    print(f"测试{'通过' if actual == expected else '失败'}")
    
    return actual == expected

if __name__ == "__main__":
    print("=" * 40)
    print("测试 test.py 中的代码逻辑")
    print("=" * 40)
    
    success = test_original_code()
    
    print("=" * 40)
    print(f"总体测试结果: {'成功' if success else '失败'}")
    print("=" * 40)
